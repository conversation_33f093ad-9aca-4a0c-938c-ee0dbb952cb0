-- =====================================================
-- PCOLLX DATABASE STRUCTURE (Updated)
-- Price Collection System Database
-- Generated on: 2025-08-09
-- Database: pcollx_db
-- Note: No foreign key constraints as requested
-- Note: Structure only - no dummy data
-- =====================================================

-- Drop database if exists and create new
DROP DATABASE IF EXISTS `pcollx_db`;
CREATE DATABASE `pcollx_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `pcollx_db`;

-- =====================================================
-- GEOGRAPHIC REFERENCE TABLES
-- =====================================================

-- Countries table (Updated structure)
CREATE TABLE `geo_countries` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `country_code` char(2) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(10) unsigned DEFAULT NULL,
  `updated_by` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_country_code` (`country_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Provinces table (Updated structure)
CREATE TABLE `geo_provinces` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `province_code` varchar(10) NOT NULL,
  `name` varchar(100) NOT NULL,
  `country_id` int(10) unsigned NOT NULL,
  `json_id` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(10) unsigned DEFAULT NULL,
  `updated_by` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_province_code` (`province_code`),
  KEY `idx_country_id` (`country_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Districts table
CREATE TABLE `geo_districts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `province_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `code` varchar(10) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- USER AND ORGANIZATION MANAGEMENT
-- =====================================================

-- System users table (Updated structure)
CREATE TABLE `dakoii_users` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `username` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` varchar(100) NOT NULL,
  `is_active` tinyint(1) DEFAULT 0,
  `created_by` int(11) unsigned DEFAULT NULL,
  `updated_by` int(11) unsigned DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Organization table (Updated structure)
CREATE TABLE `dakoii_org` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `org_code` varchar(100) NOT NULL,
  `org_name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `province_id` int(11) DEFAULT NULL,
  `country_id` int(11) DEFAULT NULL,
  `logo_path` varchar(255) DEFAULT NULL,
  `is_locationlocked` tinyint(1) NOT NULL DEFAULT 0,
  `postal_address` text DEFAULT NULL,
  `phone_numbers` text DEFAULT NULL,
  `email_addresses` text DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `license_status` varchar(50) DEFAULT NULL,
  `created_by` int(11) unsigned DEFAULT NULL,
  `updated_by` int(11) unsigned DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Application users table (Updated structure)
CREATE TABLE `users` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `org_id` int(11) NOT NULL,
  `sys_no` int(20) NOT NULL COMMENT 'system number',
  `name` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('user','guest') NOT NULL DEFAULT 'user',
  `is_admin` tinyint(5) NOT NULL,
  `is_supervisor` tinyint(5) NOT NULL,
  `position` varchar(255) DEFAULT NULL,
  `id_photo` varchar(500) DEFAULT NULL,
  `phone` varchar(200) DEFAULT NULL,
  `email` varchar(500) NOT NULL,
  `status` varchar(20) NOT NULL,
  `activation_token` varchar(255) DEFAULT NULL,
  `activation_sent_at` datetime DEFAULT NULL,
  `activated_at` datetime DEFAULT NULL,
  `created_by` varchar(200) DEFAULT NULL,
  `updated_by` varchar(200) DEFAULT NULL,
  `deleted_by` varchar(200) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- GOODS AND PRODUCTS MANAGEMENT
-- =====================================================

-- Goods groups table
CREATE TABLE `goods_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_name` varchar(255) NOT NULL,
  `group_code` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `parent_group_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Goods brands table
CREATE TABLE `goods_brands` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `brand_name` varchar(255) NOT NULL,
  `brand_code` varchar(50) DEFAULT NULL,
  `manufacturer` varchar(255) DEFAULT NULL,
  `country_of_origin` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Goods items table
CREATE TABLE `goods_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item_name` varchar(255) NOT NULL,
  `item_code` varchar(50) DEFAULT NULL,
  `group_id` int(11) DEFAULT NULL,
  `brand_id` int(11) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `unit_of_measure` varchar(50) DEFAULT NULL,
  `standard_size` varchar(100) DEFAULT NULL,
  `barcode` varchar(100) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `item_code` (`item_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- BUSINESS ENTITIES AND LOCATIONS
-- =====================================================

-- Business entities table
CREATE TABLE `business_entities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `business_name` varchar(255) NOT NULL,
  `business_type` enum('supermarket','retail_store','market','wholesaler','distributor','other') DEFAULT NULL,
  `registration_number` varchar(100) DEFAULT NULL,
  `contact_person` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `website` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Business locations table
CREATE TABLE `business_locations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `business_entity_id` int(11) NOT NULL,
  `location_name` varchar(255) NOT NULL,
  `location_code` varchar(50) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `district_id` int(11) DEFAULT NULL,
  `latitude` decimal(10,8) DEFAULT NULL,
  `longitude` decimal(11,8) DEFAULT NULL,
  `contact_person` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `operating_hours` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- PRICE COLLECTION ACTIVITIES
-- =====================================================

-- Activities table
CREATE TABLE `activities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `activity_name` varchar(255) NOT NULL,
  `activity_code` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `frequency` enum('daily','weekly','monthly','quarterly','annual') DEFAULT NULL,
  `status` enum('planned','active','completed','cancelled','suspended') DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `activity_code` (`activity_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Activity users table
CREATE TABLE `activity_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `activity_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `role` enum('coordinator','collector','supervisor','analyst') DEFAULT NULL,
  `assigned_date` date DEFAULT NULL,
  `status` enum('assigned','active','completed','removed') DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Activity business locations table
CREATE TABLE `activity_business_locations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `activity_id` int(11) NOT NULL,
  `business_location_id` int(11) NOT NULL,
  `assigned_collector_id` int(11) DEFAULT NULL,
  `collection_frequency` enum('daily','weekly','monthly') DEFAULT NULL,
  `status` enum('assigned','active','completed','suspended') DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- PRICE DATA COLLECTION
-- =====================================================

-- Price data table
CREATE TABLE `price_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `activity_id` int(11) NOT NULL,
  `business_location_id` int(11) NOT NULL,
  `goods_item_id` int(11) NOT NULL,
  `collector_id` int(11) NOT NULL,
  `collection_date` date NOT NULL,
  `collection_time` time DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `currency` varchar(10) DEFAULT 'PGK',
  `unit_size` varchar(100) DEFAULT NULL,
  `availability_status` enum('available','limited','out_of_stock','discontinued') DEFAULT 'available',
  `quality_grade` enum('premium','standard','economy','poor') DEFAULT NULL,
  `promotion_type` varchar(100) DEFAULT NULL,
  `promotion_discount` decimal(5,2) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `verified_by` int(11) DEFAULT NULL,
  `verification_date` date DEFAULT NULL,
  `status` enum('draft','submitted','verified','rejected') DEFAULT 'draft',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_price_data_date` (`collection_date`),
  KEY `idx_price_data_location` (`business_location_id`),
  KEY `idx_price_data_item` (`goods_item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Activity price collection data table (summary/aggregated data)
CREATE TABLE `activity_price_collection_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `activity_id` int(11) NOT NULL,
  `goods_item_id` int(11) NOT NULL,
  `collection_period` date NOT NULL,
  `total_locations_surveyed` int(11) DEFAULT NULL,
  `locations_with_data` int(11) DEFAULT NULL,
  `average_price` decimal(10,2) DEFAULT NULL,
  `minimum_price` decimal(10,2) DEFAULT NULL,
  `maximum_price` decimal(10,2) DEFAULT NULL,
  `price_variance` decimal(10,4) DEFAULT NULL,
  `availability_percentage` decimal(5,2) DEFAULT NULL,
  `total_records` int(11) DEFAULT NULL,
  `verified_records` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_activity_price_period` (`collection_period`),
  KEY `idx_activity_price_item` (`goods_item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- WORKPLAN MANAGEMENT
-- =====================================================

-- Workplans table
CREATE TABLE `workplans` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `workplan_name` varchar(255) NOT NULL,
  `workplan_code` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `budget_allocated` decimal(15,2) DEFAULT NULL,
  `budget_spent` decimal(15,2) DEFAULT NULL,
  `responsible_officer_id` int(11) DEFAULT NULL,
  `status` enum('draft','approved','active','completed','cancelled') DEFAULT 'draft',
  `created_by` int(11) DEFAULT NULL,
  `approved_by` int(11) DEFAULT NULL,
  `approved_date` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `workplan_code` (`workplan_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- END OF SCHEMA
-- =====================================================

-- Summary of created tables:
-- 1. Geographic Reference Tables: 3 tables (geo_countries, geo_provinces, geo_districts)
-- 2. User Management Tables: 3 tables (dakoii_users, dakoii_org, users)
-- 3. Goods Management Tables: 3 tables (goods_groups, goods_brands, goods_items)
-- 4. Business Management Tables: 2 tables (business_entities, business_locations)
-- 5. Activity Management Tables: 3 tables (activities, activity_users, activity_business_locations)
-- 6. Price Data Tables: 2 tables (price_data, activity_price_collection_data)
-- 7. Workplan Management Tables: 1 table (workplans)

-- Total: 17 tables with updated structure matching the models
-- Database ready for price collection and monitoring system
-- No dummy data included - structure only
